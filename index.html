<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
    <div id="content">
        <h1>Quiz Manager</h1>
        <a href="player-list.html">View Player List</a>
        <div id="quiz-data">
            <h2>Quiz Bank</h2>
            <div id="quiz-list"></div>
        </div>
        <div id="tip-data">
            <h2>Tip Bank</h2>
            <div id="tip-list"></div>
        </div>
        <div id="loading">Loading data...</div>
        <div id="error" style="color: red; display: none;"></div>
    </div>

    <script type="module">
        // Import the functions you need from the SDKs you need
        import { initializeApp } from "https://www.gstatic.com/firebasejs/12.2.1/firebase-app.js";
        import { getDatabase, ref, get, child } from "https://www.gstatic.com/firebasejs/12.2.1/firebase-database.js";

        // Your web app's Firebase configuration
        // For Firebase JS SDK v7.20.0 and later, measurementId is optional
        const firebaseConfig = {
            apiKey: "AIzaSyDenbfLXVnpxWpn8SpOGOGYsVx0__VDtF4",
            authDomain: "cyber-learning-game.firebaseapp.com",
            databaseURL: "https://cyber-learning-game-default-rtdb.firebaseio.com",
            projectId: "cyber-learning-game",
            storageBucket: "cyber-learning-game.firebasestorage.app",
            messagingSenderId: "276191630065",
            appId: "1:276191630065:web:5ae7e9f9cd559aba0fcf50",
            measurementId: "G-Y52FKBM536"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const database = getDatabase(app);

        // DOM elements
        const loadingElement = document.getElementById('loading');
        const errorElement = document.getElementById('error');
        const quizListElement = document.getElementById('quiz-list');
        const tipListElement = document.getElementById('tip-list');

        // Function to show error message
        function showError(message) {
            errorElement.textContent = message;
            errorElement.style.display = 'block';
            loadingElement.style.display = 'none';
        }

        // Function to hide loading indicator
        function hideLoading() {
            loadingElement.style.display = 'none';
        }

        // Function to read quiz bank data
        async function readQuizBank() {
            try {
                const dbRef = ref(database);
                const snapshot = await get(child(dbRef, 'quiz_bank'));

                if (snapshot.exists()) {
                    const quizData = snapshot.val();
                    displayQuizData(quizData);
                    console.log('Quiz Bank Data:', quizData);
                } else {
                    console.log('No quiz bank data available');
                    quizListElement.innerHTML = '<p>No quiz data found</p>';
                }
            } catch (error) {
                console.error('Error reading quiz bank:', error);
                showError('Error loading quiz data: ' + error.message);
            }
        }

        // Function to read tip bank data
        async function readTipBank() {
            try {
                const dbRef = ref(database);
                const snapshot = await get(child(dbRef, 'tip_bank'));

                if (snapshot.exists()) {
                    const tipData = snapshot.val();
                    displayTipData(tipData);
                    console.log('Tip Bank Data:', tipData);
                } else {
                    console.log('No tip bank data available');
                    tipListElement.innerHTML = '<p>No tip data found</p>';
                }
            } catch (error) {
                console.error('Error reading tip bank:', error);
                showError('Error loading tip data: ' + error.message);
            }
        }

        // Function to display quiz data
        function displayQuizData(quizData) {
            let html = '';

            if (Array.isArray(quizData)) {
                quizData.forEach((quiz, index) => {
                    if (quiz) {
                        html += formatQuizItem(quiz, `Quiz ${index + 1}`);
                    }
                });
            } else if (typeof quizData === 'object') {
                Object.keys(quizData).forEach(key => {
                    const quiz = quizData[key];
                    html += formatQuizItem(quiz, key);
                });
            }

            quizListElement.innerHTML = html || '<p>No quiz data to display</p>';
        }

        // Function to format individual quiz item
        function formatQuizItem(quiz, title) {
            let questionsHtml = '';

            if (quiz.questions && Array.isArray(quiz.questions)) {
                questionsHtml = quiz.questions.map((q, index) => {
                    let optionsHtml = '';
                    if (q.options && Array.isArray(q.options)) {
                        optionsHtml = q.options.map((option, optIndex) => {
                            const isCorrect = q.answer === optIndex;
                            const style = isCorrect ? 'color: green; font-weight: bold;' : '';
                            return `<li style="${style}">${option}</li>`;
                        }).join('');
                    }

                    let imageHtml = '';
                    if (q.questionImg) {
                        imageHtml = `<img src="${q.questionImg}" alt="Question Image" style="max-width: 300px; margin: 10px 0;">`;
                    }

                    let feedbackHtml = '';
                    if (q.feedback && Array.isArray(q.feedback)) {
                        feedbackHtml = `
                            <div style="margin-top: 10px;">
                                <strong>Feedback:</strong>
                                <ul>
                                    ${q.feedback.map(fb => `<li>${fb}</li>`).join('')}
                                </ul>
                            </div>
                        `;
                    }

                    return `
                        <div style="border-left: 3px solid #007bff; padding-left: 15px; margin: 15px 0;">
                            <h5>Question ${index + 1} (${q.id}) - ${q.type}</h5>
                            <p><strong>Question:</strong> ${q.questionText}</p>
                            ${imageHtml}
                            <div style="margin: 10px 0;">
                                <strong>Options:</strong>
                                <ol>${optionsHtml}</ol>
                            </div>
                            <p><strong>Correct Answer:</strong> Option ${q.answer + 1}</p>
                            ${feedbackHtml}
                        </div>
                    `;
                }).join('');
            }

            return `
                <div style="border: 1px solid #ccc; margin: 15px 0; padding: 15px; border-radius: 8px; background-color: #f9f9f9;">
                    <h3 style="color: #333; margin-bottom: 10px;">${title}</h3>
                    <p><strong>Quiz ID:</strong> ${quiz.quiz_id || 'N/A'}</p>
                    <p><strong>Title:</strong> ${quiz.title || 'N/A'}</p>
                    <p><strong>Module:</strong> ${quiz.module || 'N/A'}</p>
                    <div style="margin-top: 15px;">
                        <h4>Questions (${quiz.questions ? quiz.questions.length : 0}):</h4>
                        ${questionsHtml}
                    </div>
                </div>
            `;
        }

        // Function to display tip data
        function displayTipData(tipData) {
            let html = '';

            if (Array.isArray(tipData)) {
                tipData.forEach((tip, index) => {
                    if (tip) {
                        html += formatTipItem(tip, `Tip ${index + 1}`);
                    }
                });
            } else if (typeof tipData === 'object') {
                Object.keys(tipData).forEach(key => {
                    const tip = tipData[key];
                    html += formatTipItem(tip, key);
                });
            }

            tipListElement.innerHTML = html || '<p>No tip data to display</p>';
        }

        // Function to format individual tip item
        function formatTipItem(tip, title) {
            let exampleHtml = '';
            if (tip.example) {
                // Check if example is a URL
                if (tip.example.startsWith('http://') || tip.example.startsWith('https://')) {
                    exampleHtml = `<p><strong>Example:</strong> <a href="${tip.example}" target="_blank" style="color: #007bff;">${tip.example}</a></p>`;
                } else {
                    exampleHtml = `<p><strong>Example:</strong> ${tip.example}</p>`;
                }
            }

            return `
                <div style="border: 1px solid #28a745; margin: 15px 0; padding: 15px; border-radius: 8px; background-color: #f8fff9;">
                    <h3 style="color: #28a745; margin-bottom: 10px;">${title}</h3>
                    <p><strong>Concept:</strong> ${tip.conceptText || 'N/A'}</p>
                    <p><strong>Type:</strong> ${tip.conceptType || 'N/A'}</p>
                    <p><strong>Explanation:</strong> ${tip.textExplain || 'N/A'}</p>
                    ${exampleHtml}
                </div>
            `;
        }

        // Load data when page loads
        window.addEventListener('load', async () => {
            try {
                await Promise.all([readQuizBank(), readTipBank()]);
                hideLoading();
            } catch (error) {
                console.error('Error loading data:', error);
                showError('Error loading data: ' + error.message);
            }
        });
    </script>
</body>
</html>