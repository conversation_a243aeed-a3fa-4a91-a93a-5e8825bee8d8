<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quiz Manager - Dashboard</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        #content {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }

        .header h1 {
            margin: 0;
            font-size: 32px;
            font-weight: 300;
        }

        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 16px;
        }

        .nav-section {
            background: #f8f9fa;
            padding: 20px 30px;
            border-bottom: 1px solid #e9ecef;
        }

        .nav-link {
            display: inline-block;
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
        }

        .nav-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.4);
        }

        .main-content {
            padding: 30px;
        }

        .section {
            margin-bottom: 40px;
        }

        .section-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }

        .section-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin: 0;
        }

        .section-icon {
            font-size: 28px;
            margin-right: 15px;
        }

        .quiz-section .section-icon {
            color: #667eea;
        }

        .tip-section .section-icon {
            color: #28a745;
        }

        .data-container {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            min-height: 200px;
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
            font-size: 18px;
        }

        .error {
            text-align: center;
            padding: 30px;
            color: #f44336;
            font-size: 16px;
            background: #ffebee;
            border-radius: 8px;
            margin: 20px 0;
        }

        .no-data {
            text-align: center;
            padding: 50px;
            color: #666;
            font-style: italic;
        }

        /* Quiz and Tip Item Styles */
        .quiz-item, .tip-item {
            border: 1px solid #e0e0e0;
            margin: 15px 0;
            padding: 20px;
            border-radius: 12px;
            background: white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .quiz-item:hover, .tip-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.15);
        }

        .quiz-item {
            border-left: 4px solid #667eea;
        }

        .tip-item {
            border-left: 4px solid #28a745;
        }

        .item-title {
            color: #333;
            margin-bottom: 15px;
            font-size: 20px;
            font-weight: 600;
        }

        .quiz-item .item-title {
            color: #667eea;
        }

        .tip-item .item-title {
            color: #28a745;
        }

        .item-meta {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .meta-badge {
            background: #e9ecef;
            color: #495057;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 500;
        }

        .quiz-item .meta-badge {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
        }

        .tip-item .meta-badge {
            background: rgba(40, 167, 69, 0.1);
            color: #28a745;
        }

        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .header {
                padding: 20px;
            }

            .header h1 {
                font-size: 28px;
            }

            .nav-section {
                padding: 15px 20px;
            }

            .main-content {
                padding: 20px;
            }

            .section-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .section-title {
                font-size: 20px;
            }

            .item-meta {
                flex-direction: column;
                gap: 8px;
            }
        }
    </style>
</head>
<body>
    <div id="content">
        <div class="header">
            <h1>🎯 Quiz Manager</h1>
            <p>Manage quizzes, tips, and monitor player progress</p>
        </div>

        <div class="nav-section">
            <a href="player-list.html" class="nav-link">👥 View Player List</a>
        </div>

        <div class="main-content">
            <div class="section quiz-section">
                <div class="section-header">
                    <span class="section-icon">📝</span>
                    <h2 class="section-title">Quiz Bank</h2>
                </div>
                <div class="data-container">
                    <div id="quiz-list">
                        <div class="loading">📊 Loading quiz data...</div>
                    </div>
                </div>
            </div>

            <div class="section tip-section">
                <div class="section-header">
                    <span class="section-icon">💡</span>
                    <h2 class="section-title">Tip Bank</h2>
                </div>
                <div class="data-container">
                    <div id="tip-list">
                        <div class="loading">📊 Loading tip data...</div>
                    </div>
                </div>
            </div>
        </div>

        <div id="error" style="display: none;"></div>
    </div>

    <script type="module">
        // Import the functions you need from the SDKs you need
        import { initializeApp } from "https://www.gstatic.com/firebasejs/12.2.1/firebase-app.js";
        import { getDatabase, ref, onValue, off } from "https://www.gstatic.com/firebasejs/12.2.1/firebase-database.js";

        // Your web app's Firebase configuration
        // For Firebase JS SDK v7.20.0 and later, measurementId is optional
        const firebaseConfig = {
            apiKey: "AIzaSyDenbfLXVnpxWpn8SpOGOGYsVx0__VDtF4",
            authDomain: "cyber-learning-game.firebaseapp.com",
            databaseURL: "https://cyber-learning-game-default-rtdb.firebaseio.com",
            projectId: "cyber-learning-game",
            storageBucket: "cyber-learning-game.firebasestorage.app",
            messagingSenderId: "276191630065",
            appId: "1:276191630065:web:5ae7e9f9cd559aba0fcf50",
            measurementId: "G-Y52FKBM536"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const database = getDatabase(app);

        // DOM elements
        const errorElement = document.getElementById('error');
        const quizListElement = document.getElementById('quiz-list');
        const tipListElement = document.getElementById('tip-list');

        // Function to show error message
        function showError(message) {
            errorElement.innerHTML = `<div class="error">${message}</div>`;
            errorElement.style.display = 'block';
        }

        // Function to hide loading indicator
        function hideLoading() {
            // Loading is handled per section now
        }

        // Real-time listeners
        let quizBankRef = null;
        let tipBankRef = null;
        let isListeningQuiz = false;
        let isListeningTip = false;

        // Function to start listening to quiz bank data
        function startListeningQuizBank() {
            if (isListeningQuiz) return;

            try {
                quizBankRef = ref(database, 'quiz_bank');

                onValue(quizBankRef, (snapshot) => {
                    console.log('Quiz bank update received');

                    if (snapshot.exists()) {
                        const quizData = snapshot.val();
                        displayQuizData(quizData);
                        console.log('Quiz Bank Data:', quizData);
                    } else {
                        console.log('No quiz bank data available');
                        quizListElement.innerHTML = '<p>No quiz data found</p>';
                    }
                }, (error) => {
                    console.error('Error listening to quiz bank:', error);
                    showError('Error loading quiz data: ' + error.message);
                });

                isListeningQuiz = true;
                console.log('Started listening to quiz bank changes');

            } catch (error) {
                console.error('Error setting up quiz bank listener:', error);
                showError('Error setting up quiz data connection: ' + error.message);
            }
        }

        // Function to start listening to tip bank data
        function startListeningTipBank() {
            if (isListeningTip) return;

            try {
                tipBankRef = ref(database, 'tip_bank');

                onValue(tipBankRef, (snapshot) => {
                    console.log('Tip bank update received');

                    if (snapshot.exists()) {
                        const tipData = snapshot.val();
                        displayTipData(tipData);
                        console.log('Tip Bank Data:', tipData);
                    } else {
                        console.log('No tip bank data available');
                        tipListElement.innerHTML = '<p>No tip data found</p>';
                    }
                }, (error) => {
                    console.error('Error listening to tip bank:', error);
                    showError('Error loading tip data: ' + error.message);
                });

                isListeningTip = true;
                console.log('Started listening to tip bank changes');

            } catch (error) {
                console.error('Error setting up tip bank listener:', error);
                showError('Error setting up tip data connection: ' + error.message);
            }
        }

        // Function to stop all listeners
        function stopAllListeners() {
            if (quizBankRef && isListeningQuiz) {
                off(quizBankRef);
                isListeningQuiz = false;
                console.log('Stopped listening to quiz bank changes');
            }

            if (tipBankRef && isListeningTip) {
                off(tipBankRef);
                isListeningTip = false;
                console.log('Stopped listening to tip bank changes');
            }
        }

        // Function to display quiz data
        function displayQuizData(quizData) {
            let html = '';

            if (Array.isArray(quizData)) {
                quizData.forEach((quiz, index) => {
                    if (quiz) {
                        html += formatQuizItem(quiz, `Quiz ${index + 1}`);
                    }
                });
            } else if (typeof quizData === 'object') {
                Object.keys(quizData).forEach(key => {
                    const quiz = quizData[key];
                    html += formatQuizItem(quiz, key);
                });
            }

            quizListElement.innerHTML = html || '<div class="no-data">No quiz data to display</div>';
        }

        // Function to format individual quiz item
        function formatQuizItem(quiz, title) {
            const questionCount = quiz.questions ? quiz.questions.length : 0;

            let questionsHtml = '';
            if (quiz.questions && Array.isArray(quiz.questions)) {
                questionsHtml = quiz.questions.map((q, index) => {
                    let optionsHtml = '';
                    if (q.options && Array.isArray(q.options)) {
                        optionsHtml = q.options.map((option, optIndex) => {
                            const isCorrect = q.answer === optIndex;
                            const style = isCorrect ? 'color: #4CAF50; font-weight: bold;' : 'color: #666;';
                            return `<li style="${style}">${option}</li>`;
                        }).join('');
                    }

                    let imageHtml = '';
                    if (q.questionImg) {
                        imageHtml = `<img src="${q.questionImg}" alt="Question Image" style="max-width: 100%; height: auto; margin: 10px 0; border-radius: 8px;">`;
                    }

                    let feedbackHtml = '';
                    if (q.feedback && Array.isArray(q.feedback)) {
                        feedbackHtml = `
                            <div style="margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 6px;">
                                <strong style="color: #495057;">💡 Feedback:</strong>
                                <ul style="margin: 8px 0 0 20px; color: #666;">
                                    ${q.feedback.map(fb => `<li style="margin: 4px 0;">${fb}</li>`).join('')}
                                </ul>
                            </div>
                        `;
                    }

                    return `
                        <div style="border-left: 3px solid #667eea; padding: 15px; margin: 15px 0; background: #f8f9fa; border-radius: 8px;">
                            <h5 style="color: #667eea; margin: 0 0 10px 0; font-size: 16px;">❓ Question ${index + 1} (${q.id}) - ${q.type}</h5>
                            <p style="margin: 10px 0; color: #333; font-weight: 500;"><strong>Question:</strong> ${q.questionText}</p>
                            ${imageHtml}
                            <div style="margin: 15px 0;">
                                <strong style="color: #495057;">📝 Options:</strong>
                                <ol style="margin: 8px 0 0 20px;">${optionsHtml}</ol>
                            </div>
                            <p style="margin: 10px 0; color: #4CAF50; font-weight: 500;"><strong>✅ Correct Answer:</strong> Option ${q.answer + 1}</p>
                            ${feedbackHtml}
                        </div>
                    `;
                }).join('');
            }

            return `
                <div class="quiz-item">
                    <h3 class="item-title">📝 ${title}</h3>
                    <div class="item-meta">
                        <span class="meta-badge">ID: ${quiz.quiz_id || 'N/A'}</span>
                        <span class="meta-badge">Title: ${quiz.title || 'N/A'}</span>
                        <span class="meta-badge">Module: ${quiz.module || 'N/A'}</span>
                        <span class="meta-badge">${questionCount} Questions</span>
                    </div>
                    <div style="margin-top: 20px;">
                        <h4 style="color: #495057; margin-bottom: 15px; font-size: 18px;">📋 Questions:</h4>
                        ${questionsHtml || '<p style="color: #666; font-style: italic; text-align: center; padding: 20px;">No questions available</p>'}
                    </div>
                </div>
            `;
        }

        // Function to display tip data
        function displayTipData(tipData) {
            let html = '';

            if (Array.isArray(tipData)) {
                tipData.forEach((tip, index) => {
                    if (tip) {
                        html += formatTipItem(tip, `Tip ${index + 1}`);
                    }
                });
            } else if (typeof tipData === 'object') {
                Object.keys(tipData).forEach(key => {
                    const tip = tipData[key];
                    html += formatTipItem(tip, key);
                });
            }

            tipListElement.innerHTML = html || '<div class="no-data">No tip data to display</div>';
        }

        // Function to format individual tip item
        function formatTipItem(tip, title) {
            let exampleHtml = '';
            if (tip.example) {
                // Check if example is a URL
                if (tip.example.startsWith('http://') || tip.example.startsWith('https://')) {
                    exampleHtml = `
                        <div style="margin-top: 15px; padding: 10px; background: #e8f5e8; border-radius: 6px;">
                            <strong style="color: #28a745;">🔗 Example:</strong>
                            <a href="${tip.example}" target="_blank" style="color: #28a745; text-decoration: none; font-weight: 500;">${tip.example}</a>
                        </div>
                    `;
                } else {
                    exampleHtml = `
                        <div style="margin-top: 15px; padding: 10px; background: #e8f5e8; border-radius: 6px;">
                            <strong style="color: #28a745;">💡 Example:</strong>
                            <span style="color: #495057;">${tip.example}</span>
                        </div>
                    `;
                }
            }

            return `
                <div class="tip-item">
                    <h3 class="item-title">💡 ${title}</h3>
                    <div class="item-meta">
                        <span class="meta-badge">Type: ${tip.conceptType || 'N/A'}</span>
                    </div>
                    <div style="margin-top: 15px;">
                        <p style="margin: 10px 0; color: #333;"><strong style="color: #28a745;">📝 Concept:</strong> ${tip.conceptText || 'N/A'}</p>
                        <p style="margin: 10px 0; color: #666; line-height: 1.6;"><strong style="color: #28a745;">📖 Explanation:</strong> ${tip.textExplain || 'N/A'}</p>
                        ${exampleHtml}
                    </div>
                </div>
            `;
        }

        // Start listening when page loads
        window.addEventListener('load', () => {
            try {
                startListeningQuizBank();
                startListeningTipBank();
                hideLoading();
            } catch (error) {
                console.error('Error setting up listeners:', error);
                showError('Error setting up real-time connections: ' + error.message);
            }
        });

        // Handle page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                console.log('Page hidden - keeping listeners active for real-time updates');
                // Keep listeners active even when page is hidden for real-time updates
            } else {
                console.log('Page visible - ensuring listeners are active');
                if (!isListeningQuiz) {
                    startListeningQuizBank();
                }
                if (!isListeningTip) {
                    startListeningTipBank();
                }
            }
        });

        // Clean up listeners when page is unloaded
        window.addEventListener('beforeunload', () => {
            stopAllListeners();
        });

        // Handle online/offline status
        window.addEventListener('online', () => {
            console.log('Connection restored');
            if (!isListeningQuiz) {
                startListeningQuizBank();
            }
            if (!isListeningTip) {
                startListeningTipBank();
            }
        });

        window.addEventListener('offline', () => {
            console.log('Connection lost');
            // Listeners will automatically reconnect when online
        });
    </script>
</body>
</html>