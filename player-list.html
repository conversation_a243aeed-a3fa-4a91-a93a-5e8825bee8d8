<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Player List - Quiz Manager</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        #content {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }

        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 300;
        }

        .back-link {
            position: absolute;
            left: 30px;
            top: 50%;
            transform: translateY(-50%);
            color: white;
            text-decoration: none;
            font-size: 14px;
            padding: 8px 16px;
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 20px;
            transition: all 0.3s ease;
        }

        .back-link:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-50%) translateX(-2px);
        }

        .stats-summary {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 20px;
            margin: 20px;
            border-radius: 10px;
            text-align: center;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            display: block;
        }

        .stat-label {
            font-size: 12px;
            opacity: 0.9;
            margin-top: 5px;
        }

        #player-list {
            padding: 20px;
        }

        .player-card {
            border: 1px solid #e0e0e0;
            margin: 15px 0;
            padding: 20px;
            border-radius: 12px;
            background: white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            color: inherit;
            display: block;
        }

        .player-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.15);
            border-color: #667eea;
        }

        .player-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .player-id {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }

        .player-badges {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .badge {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            color: white;
        }

        .badge-coins { background: #4CAF50; }
        .badge-tokens { background: #2196F3; }
        .badge-quizzes { background: #FF9800; }

        .player-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .stat-box {
            text-align: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .stat-value {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }

        .stat-desc {
            font-size: 11px;
            color: #666;
            margin-top: 2px;
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
            font-size: 18px;
        }

        .error {
            text-align: center;
            padding: 50px;
            color: #f44336;
            font-size: 16px;
        }

        .no-data {
            text-align: center;
            padding: 50px;
            color: #666;
            font-style: italic;
        }

        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .header {
                padding: 20px;
            }

            .header h1 {
                font-size: 24px;
            }

            .back-link {
                position: static;
                transform: none;
                display: inline-block;
                margin-bottom: 15px;
            }

            .player-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .stats-summary {
                margin: 10px;
                padding: 15px;
                grid-template-columns: repeat(2, 1fr);
            }

            #player-list {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div id="content">
        <div class="header">
            <a href="index.html" class="back-link">← Back to Quiz Manager</a>
            <h1>👥 Player List</h1>
        </div>

        <div id="stats-container"></div>

        <div id="player-list">
            <div class="loading">📊 Loading players...</div>
        </div>
    </div>

    <script type="module">
        // Import Firebase functions
        import { initializeApp } from "https://www.gstatic.com/firebasejs/12.2.1/firebase-app.js";
        import { getDatabase, ref, get, child } from "https://www.gstatic.com/firebasejs/12.2.1/firebase-database.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDenbfLXVnpxWpn8SpOGOGYsVx0__VDtF4",
            authDomain: "cyber-learning-game.firebaseapp.com",
            databaseURL: "https://cyber-learning-game-default-rtdb.firebaseio.com",
            projectId: "cyber-learning-game",
            storageBucket: "cyber-learning-game.firebasestorage.app",
            messagingSenderId: "276191630065",
            appId: "1:276191630065:web:5ae7e9f9cd559aba0fcf50",
            measurementId: "G-Y52FKBM536"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const database = getDatabase(app);

        // Load player data
        async function loadPlayers() {
            try {
                const dbRef = ref(database);
                const snapshot = await get(child(dbRef, 'users'));

                if (snapshot.exists()) {
                    const userData = snapshot.val();
                    displayPlayers(userData);
                } else {
                    showNoData();
                }
            } catch (error) {
                console.error('Error loading players:', error);
                showError('Error loading player data: ' + error.message);
            }
        }

        function displayPlayers(userData) {
            const playerListDiv = document.getElementById('player-list');
            const statsContainer = document.getElementById('stats-container');

            if (!userData || Object.keys(userData).length === 0) {
                showNoData();
                return;
            }

            // Calculate summary statistics
            const players = Object.entries(userData);
            const totalPlayers = players.length;
            const totalCoins = players.reduce((sum, [_, user]) => sum + (user.totalCoins || 0), 0);
            const totalTokens = players.reduce((sum, [_, user]) => sum + (user.totalTokens || 0), 0);
            const totalQuizzes = players.reduce((sum, [_, user]) => {
                return sum + (user.quizzes ? Object.keys(user.quizzes).length : 0);
            }, 0);

            // Display summary stats
            statsContainer.innerHTML = `
                <div class="stats-summary">
                    <div class="stat-item">
                        <span class="stat-number">${totalPlayers}</span>
                        <div class="stat-label">Total Players</div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">${totalCoins.toLocaleString()}</span>
                        <div class="stat-label">Total Coins</div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">${totalTokens.toLocaleString()}</span>
                        <div class="stat-label">Total Tokens</div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">${totalQuizzes}</span>
                        <div class="stat-label">Quizzes Completed</div>
                    </div>
                </div>
            `;

            // Sort players by total activity (coins + tokens)
            const sortedPlayers = players.sort((a, b) => {
                const scoreA = (a[1].totalCoins || 0) + (a[1].totalTokens || 0);
                const scoreB = (b[1].totalCoins || 0) + (b[1].totalTokens || 0);
                return scoreB - scoreA;
            });

            // Display player list
            let html = '';
            sortedPlayers.forEach(([userId, user]) => {
                html += createPlayerCard(userId, user);
            });

            playerListDiv.innerHTML = html;
        }

        function createPlayerCard(userId, user) {
            const shortUserId = userId.replace('user_', '').substring(0, 12) + '...';
            const fullUserId = userId.replace('user_', '');
            const quizCount = user.quizzes ? Object.keys(user.quizzes).length : 0;
            const totalCoins = user.totalCoins || 0;
            const totalTokens = user.totalTokens || 0;

            // Calculate average score
            let avgScore = 0;
            let totalQuestions = 0;
            let correctAnswers = 0;

            if (user.quizzes) {
                Object.values(user.quizzes).forEach(quiz => {
                    if (quiz.answers) {
                        totalQuestions += quiz.answers.length;
                        correctAnswers += quiz.answers.filter(answer => answer.isCorrect).length;
                    }
                });
                avgScore = totalQuestions > 0 ? Math.round((correctAnswers / totalQuestions) * 100) : 0;
            }

            // Get last activity
            let lastActivity = 'Never';
            if (user.quizzes) {
                const timestamps = Object.values(user.quizzes)
                    .map(quiz => quiz.answeredTime)
                    .filter(time => time)
                    .sort((a, b) => b - a);

                if (timestamps.length > 0) {
                    lastActivity = formatTimestamp(timestamps[0]);
                }
            }

            return `
                <a href="player-data.html?userId=${encodeURIComponent(userId)}" class="player-card">
                    <div class="player-header">
                        <div class="player-id">👤 ${shortUserId}</div>
                        <div class="player-badges">
                            <span class="badge badge-coins">🪙 ${totalCoins}</span>
                            <span class="badge badge-tokens">🎯 ${totalTokens}</span>
                            <span class="badge badge-quizzes">📝 ${quizCount}</span>
                        </div>
                    </div>

                    <div class="player-stats">
                        <div class="stat-box">
                            <div class="stat-value">${avgScore}%</div>
                            <div class="stat-desc">Average Score</div>
                        </div>
                        <div class="stat-box">
                            <div class="stat-value">${correctAnswers}/${totalQuestions}</div>
                            <div class="stat-desc">Correct Answers</div>
                        </div>
                        <div class="stat-box">
                            <div class="stat-value">${lastActivity}</div>
                            <div class="stat-desc">Last Activity</div>
                        </div>
                    </div>
                </a>
            `;
        }

        function formatTimestamp(timestamp) {
            try {
                const date = new Date(timestamp);
                if (isNaN(date.getTime())) return 'Invalid';

                const now = new Date();
                const diffMs = now - date;
                const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
                const diffHours = Math.floor(diffMs / (1000 * 60 * 60));

                if (diffDays > 7) {
                    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
                } else if (diffDays > 0) {
                    return `${diffDays}d ago`;
                } else if (diffHours > 0) {
                    return `${diffHours}h ago`;
                } else {
                    return 'Recent';
                }
            } catch (error) {
                return 'Unknown';
            }
        }

        function showNoData() {
            document.getElementById('player-list').innerHTML =
                '<div class="no-data">No player data available</div>';
            document.getElementById('stats-container').innerHTML = '';
        }

        function showError(message) {
            document.getElementById('player-list').innerHTML =
                `<div class="error">${message}</div>`;
            document.getElementById('stats-container').innerHTML = '';
        }

        // Load players when page loads
        window.addEventListener('load', loadPlayers);
    </script>
</body>
</html>