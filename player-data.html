<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Player Data - Quiz Manager</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                margin: 0;
                padding: 20px;
                background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                min-height: 100vh;
            }

            #content {
                max-width: 1200px;
                margin: 0 auto;
                background: white;
                border-radius: 15px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.1);
                overflow: hidden;
            }

            .header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 30px;
                text-align: center;
                position: relative;
            }

            .header h1 {
                margin: 0;
                font-size: 28px;
                font-weight: 300;
            }

            .back-link {
                position: absolute;
                left: 30px;
                top: 50%;
                transform: translateY(-50%);
                color: white;
                text-decoration: none;
                font-size: 14px;
                padding: 8px 16px;
                border: 1px solid rgba(255,255,255,0.3);
                border-radius: 20px;
                transition: all 0.3s ease;
            }

            .player-not-found {
                text-align: center;
                padding: 50px;
                color: #666;
            }

            .player-not-found h2 {
                color: #f44336;
                margin-bottom: 20px;
            }

            .back-link:hover {
                background: rgba(255,255,255,0.2);
                transform: translateY(-50%) translateX(-2px);
            }

            #player-data {
                padding: 30px;
            }

            .loading {
                text-align: center;
                padding: 50px;
                color: #666;
                font-size: 18px;
            }

            @media (max-width: 768px) {
                body {
                    padding: 10px;
                }

                .header {
                    padding: 20px;
                }

                .header h1 {
                    font-size: 24px;
                }

                .back-link {
                    position: static;
                    transform: none;
                    display: inline-block;
                    margin-bottom: 15px;
                }

                #player-data {
                    padding: 20px;
                }
            }
        </style>
    </head>
    <body>
        <div id="content">
            <div class="header">
                <a href="player-list.html" class="back-link">← Back to Player List</a>
                <h1 id="page-title">🎮 Player Details</h1>
            </div>
            <div id="player-data">
                <div class="loading">📊 Loading player data...</div>
            </div>
        </div>
    </body>
    <script type="module">
            // Import the functions you need from the SDKs you need
            import { initializeApp } from "https://www.gstatic.com/firebasejs/12.2.1/firebase-app.js";
            import { getDatabase, ref, get, child } from "https://www.gstatic.com/firebasejs/12.2.1/firebase-database.js";

            // Your web app's Firebase configuration
            // For Firebase JS SDK v7.20.0 and later, measurementId is optional
            const firebaseConfig = {
                apiKey: "AIzaSyDenbfLXVnpxWpn8SpOGOGYsVx0__VDtF4",
                authDomain: "cyber-learning-game.firebaseapp.com",
                databaseURL: "https://cyber-learning-game-default-rtdb.firebaseio.com",
                projectId: "cyber-learning-game",
                storageBucket: "cyber-learning-game.firebasestorage.app",
                messagingSenderId: "276191630065",
                appId: "1:276191630065:web:5ae7e9f9cd559aba0fcf50",
                measurementId: "G-Y52FKBM536"
            };

            // Initialize Firebase
            const app = initializeApp(firebaseConfig);
            const database = getDatabase(app);

            // Get userId from URL parameters
            function getUserIdFromUrl() {
                const urlParams = new URLSearchParams(window.location.search);
                return urlParams.get('userId');
            }

            // Load specific player data
            async function loadPlayerData() {
                const userId = getUserIdFromUrl();

                if (!userId) {
                    showPlayerNotFound('No player ID specified in URL');
                    return;
                }

                try {
                    const dbRef = ref(database);
                    const snapshot = await get(child(dbRef, 'users'));

                    if (snapshot.exists()) {
                        const allUserData = snapshot.val();
                        const playerData = allUserData[userId];

                        if (playerData) {
                            displayUserData(userId, playerData);
                        } else {
                            showPlayerNotFound(`Player with ID "${userId}" not found`);
                        }
                    } else {
                        showPlayerNotFound('No user data available');
                    }
                } catch (error) {
                    console.error('Error loading player data:', error);
                    showPlayerNotFound('Error loading player data: ' + error.message);
                }
            }

            function showPlayerNotFound(message) {
                const playerDataDiv = document.getElementById('player-data');
                const pageTitle = document.getElementById('page-title');

                pageTitle.textContent = '❌ Player Not Found';
                playerDataDiv.innerHTML = `
                    <div class="player-not-found">
                        <h2>Player Not Found</h2>
                        <p>${message}</p>
                        <a href="player-list.html" style="color: #667eea; text-decoration: none; font-weight: bold;">
                            ← Return to Player List
                        </a>
                    </div>
                `;
            }

            function displayUserData(userId, userData) {
                const playerDataDiv = document.getElementById('player-data');
                const pageTitle = document.getElementById('page-title');

                if (!userData) {
                    showPlayerNotFound('No data available for this player');
                    return;
                }

                // Update page title with player ID
                const shortUserId = userId.replace('user_', '').substring(0, 12) + '...';
                pageTitle.textContent = `🎮 ${shortUserId}`;

                let html = '<div style="font-family: Arial, sans-serif;">';

                // Add player summary statistics
                const quizCount = userData.quizzes ? Object.keys(userData.quizzes).length : 0;
                const totalCoins = userData.totalCoins || 0;
                const totalTokens = userData.totalTokens || 0;

                // Calculate overall performance
                let totalQuestions = 0;
                let correctAnswers = 0;
                let totalTokensEarned = 0;

                if (userData.quizzes) {
                    Object.values(userData.quizzes).forEach(quiz => {
                        if (quiz.answers) {
                            totalQuestions += quiz.answers.length;
                            correctAnswers += quiz.answers.filter(answer => answer.isCorrect).length;
                        }
                        totalTokensEarned += quiz.tokensEarned || 0;
                    });
                }

                const avgScore = totalQuestions > 0 ? Math.round((correctAnswers / totalQuestions) * 100) : 0;

                html += `
                    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 12px; margin-bottom: 25px;">
                        <div style="text-align: center; margin-bottom: 20px;">
                            <h2 style="margin: 0; font-size: 24px;">📊 Player Overview</h2>
                            <p style="margin: 5px 0 0 0; opacity: 0.9; font-size: 14px;">Player ID: ${userId.replace('user_', '')}</p>
                        </div>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 20px; text-align: center;">
                            <div>
                                <div style="font-size: 28px; font-weight: bold;">${totalCoins}</div>
                                <div style="font-size: 12px; opacity: 0.9;">🪙 Total Coins</div>
                            </div>
                            <div>
                                <div style="font-size: 28px; font-weight: bold;">${totalTokens}</div>
                                <div style="font-size: 12px; opacity: 0.9;">🎯 Total Tokens</div>
                            </div>
                            <div>
                                <div style="font-size: 28px; font-weight: bold;">${quizCount}</div>
                                <div style="font-size: 12px; opacity: 0.9;">📝 Quizzes Taken</div>
                            </div>
                            <div>
                                <div style="font-size: 28px; font-weight: bold;">${avgScore}%</div>
                                <div style="font-size: 12px; opacity: 0.9;">📈 Average Score</div>
                            </div>
                            <div>
                                <div style="font-size: 28px; font-weight: bold;">${correctAnswers}/${totalQuestions}</div>
                                <div style="font-size: 12px; opacity: 0.9;">✅ Correct Answers</div>
                            </div>
                        </div>
                    </div>
                `;

                // Display user data
                html += formatUserData(userId, userData);

                html += '</div>';
                playerDataDiv.innerHTML = html;
            }

            function formatUserData(userId, user) {
                let html = '';

                if (user.quizzes && Object.keys(user.quizzes).length > 0) {
                    html += '<div style="margin-top: 0;">';
                    html += '<h3 style="color: #333; margin-bottom: 20px; font-size: 20px; text-align: center;">📋 Quiz History</h3>';

                    // Sort quizzes by answered time (most recent first)
                    const sortedQuizzes = Object.entries(user.quizzes).sort((a, b) => {
                        return (b[1].answeredTime || 0) - (a[1].answeredTime || 0);
                    });

                    sortedQuizzes.forEach(([quizKey, quiz]) => {
                        html += formatQuizData(quizKey, quiz);
                    });

                    html += '</div>';
                } else {
                    html += `
                        <div style="text-align: center; padding: 50px; color: #666; font-style: italic; background: #f9f9f9; border-radius: 12px; margin-top: 20px;">
                            <h3 style="color: #999; margin-bottom: 10px;">📝 No Quiz Data</h3>
                            <p>This player hasn't completed any quizzes yet.</p>
                        </div>
                    `;
                }

                return html;
            }

            function formatQuizData(quizKey, quiz) {
                const shortQuizKey = quizKey.length > 20 ? quizKey.substring(0, 20) + '...' : quizKey;
                const answeredTime = quiz.answeredTime ? formatTimestamp(quiz.answeredTime) : 'Unknown';
                const tokensEarned = quiz.tokensEarned || 0;
                const quizId = quiz.quizId || 'N/A';
                const startedFrom = quiz.staredFrom || quiz.startedFrom || 'Unknown';

                // Calculate score
                const answers = quiz.answers || [];
                const correctAnswers = answers.filter(answer => answer.isCorrect).length;
                const totalQuestions = answers.length;
                const scorePercentage = totalQuestions > 0 ? Math.round((correctAnswers / totalQuestions) * 100) : 0;

                // Determine score color
                let scoreColor = '#f44336'; // Red for low scores
                if (scorePercentage >= 80) scoreColor = '#4CAF50'; // Green for high scores
                else if (scorePercentage >= 60) scoreColor = '#FF9800'; // Orange for medium scores

                let html = `
                    <div style="border-left: 4px solid ${scoreColor}; background: #f9f9f9; margin: 10px 0; padding: 15px; border-radius: 8px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px; flex-wrap: wrap;">
                            <div>
                                <strong style="color: #333;">Quiz ${quizId}</strong>
                                <span style="color: #666; font-size: 12px; margin-left: 10px;">ID: ${shortQuizKey}</span>
                            </div>
                            <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                                <span style="background: ${scoreColor}; color: white; padding: 3px 8px; border-radius: 12px; font-size: 11px; font-weight: bold;">
                                    ${correctAnswers}/${totalQuestions} (${scorePercentage}%)
                                </span>
                                <span style="background: #9C27B0; color: white; padding: 3px 8px; border-radius: 12px; font-size: 11px; font-weight: bold;">
                                    +${tokensEarned} tokens
                                </span>
                            </div>
                        </div>

                        <div style="display: flex; justify-content: space-between; align-items: center; font-size: 12px; color: #666; flex-wrap: wrap;">
                            <span>📅 ${answeredTime}</span>
                            <span>🚀 Started from: ${startedFrom}</span>
                        </div>

                        ${formatAnswerDetails(answers)}
                    </div>
                `;

                return html;
            }

            function formatAnswerDetails(answers) {
                if (!answers || answers.length === 0) {
                    return '<p style="color: #666; font-size: 12px; margin-top: 10px;">No answer details available</p>';
                }

                let html = '<div style="margin-top: 10px; padding-top: 10px; border-top: 1px solid #e0e0e0;">';
                html += '<details style="cursor: pointer;">';
                html += '<summary style="font-size: 12px; color: #666; font-weight: bold;">📝 Answer Details</summary>';
                html += '<div style="margin-top: 8px; display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 8px;">';

                answers.forEach(answer => {
                    const isCorrect = answer.isCorrect;
                    const icon = isCorrect ? '✅' : '❌';
                    const bgColor = isCorrect ? '#e8f5e8' : '#ffeaea';
                    const borderColor = isCorrect ? '#4CAF50' : '#f44336';

                    html += `
                        <div style="background: ${bgColor}; border: 1px solid ${borderColor}; padding: 8px; border-radius: 6px; font-size: 11px;">
                            <div style="font-weight: bold; margin-bottom: 4px;">${icon} ${answer.questionId}</div>
                            <div>Correct: ${answer.answer}, User: ${answer.userAnswer}</div>
                        </div>
                    `;
                });

                html += '</div></details></div>';
                return html;
            }

            function formatTimestamp(timestamp) {
                try {
                    const date = new Date(timestamp);

                    // Check if the date is valid
                    if (isNaN(date.getTime())) {
                        return 'Invalid date';
                    }

                    const now = new Date();
                    const diffMs = now - date;
                    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
                    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
                    const diffMinutes = Math.floor(diffMs / (1000 * 60));

                    // Format relative time
                    let relativeTime = '';
                    if (diffDays > 0) {
                        relativeTime = `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
                    } else if (diffHours > 0) {
                        relativeTime = `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
                    } else if (diffMinutes > 0) {
                        relativeTime = `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
                    } else {
                        relativeTime = 'Just now';
                    }

                    // Format absolute time
                    const options = {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit',
                        hour12: true
                    };
                    const absoluteTime = date.toLocaleDateString('en-US', options);

                    return `${relativeTime} (${absoluteTime})`;
                } catch (error) {
                    console.error('Error formatting timestamp:', error);
                    return 'Invalid date';
                }
            }

            // Load player data when page loads
            window.addEventListener('load', loadPlayerData);

    </script>
</html>