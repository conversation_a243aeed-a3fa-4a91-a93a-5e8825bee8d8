<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Document</title>
    </head>
    <body>
        <div id="content">
            <h1>Player Data</h1>
            <div id="player-data"></div>
        </div>
    </body>
    <script type="module">
            // Import the functions you need from the SDKs you need
            import { initializeApp } from "https://www.gstatic.com/firebasejs/12.2.1/firebase-app.js";
            import { getDatabase, ref, get, child } from "https://www.gstatic.com/firebasejs/12.2.1/firebase-database.js";

            // Your web app's Firebase configuration
            // For Firebase JS SDK v7.20.0 and later, measurementId is optional
            const firebaseConfig = {
                apiKey: "AIzaSyDenbfLXVnpxWpn8SpOGOGYsVx0__VDtF4",
                authDomain: "cyber-learning-game.firebaseapp.com",
                databaseURL: "https://cyber-learning-game-default-rtdb.firebaseio.com",
                projectId: "cyber-learning-game",
                storageBucket: "cyber-learning-game.firebasestorage.app",
                messagingSenderId: "276191630065",
                appId: "1:276191630065:web:5ae7e9f9cd559aba0fcf50",
                measurementId: "G-Y52FKBM536"
            };

            // Initialize Firebase
            const app = initializeApp(firebaseConfig);
            const database = getDatabase(app);

            // retrive player data from firebase under "users" and reload page when data is updated
            const dbRef = ref(database);
            get(child(dbRef, 'users')).then((snapshot) => {
                if (snapshot.exists()) {
                    const userData = snapshot.val();
                    console.log(userData);
                    // add userData to page   
                    displayUserData(userData);
                } else {
                    console.log('No data available');
                }
            }).catch((error) => {
                console.error(error);
            });    

            function displayUserData(userData) {
                
            }

    </script>
</html>