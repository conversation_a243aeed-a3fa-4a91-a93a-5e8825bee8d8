<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Player Data - Quiz Manager</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                margin: 0;
                padding: 20px;
                background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                min-height: 100vh;
            }

            #content {
                max-width: 1200px;
                margin: 0 auto;
                background: white;
                border-radius: 15px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.1);
                overflow: hidden;
            }

            .header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 30px;
                text-align: center;
                position: relative;
            }

            .header h1 {
                margin: 0;
                font-size: 28px;
                font-weight: 300;
            }

            .back-link {
                position: absolute;
                left: 30px;
                top: 50%;
                transform: translateY(-50%);
                color: white;
                text-decoration: none;
                font-size: 14px;
                padding: 8px 16px;
                border: 1px solid rgba(255,255,255,0.3);
                border-radius: 20px;
                transition: all 0.3s ease;
            }

            .back-link:hover {
                background: rgba(255,255,255,0.2);
                transform: translateY(-50%) translateX(-2px);
            }

            #player-data {
                padding: 30px;
            }

            .loading {
                text-align: center;
                padding: 50px;
                color: #666;
                font-size: 18px;
            }

            @media (max-width: 768px) {
                body {
                    padding: 10px;
                }

                .header {
                    padding: 20px;
                }

                .header h1 {
                    font-size: 24px;
                }

                .back-link {
                    position: static;
                    transform: none;
                    display: inline-block;
                    margin-bottom: 15px;
                }

                #player-data {
                    padding: 20px;
                }
            }
        </style>
    </head>
    <body>
        <div id="content">
            <div class="header">
                <a href="index.html" class="back-link">← Back to Quiz Manager</a>
                <h1>🎮 Player Data Dashboard</h1>
            </div>
            <div id="player-data">
                <div class="loading">📊 Loading player data...</div>
            </div>
        </div>
    </body>
    <script type="module">
            // Import the functions you need from the SDKs you need
            import { initializeApp } from "https://www.gstatic.com/firebasejs/12.2.1/firebase-app.js";
            import { getDatabase, ref, get, child } from "https://www.gstatic.com/firebasejs/12.2.1/firebase-database.js";

            // Your web app's Firebase configuration
            // For Firebase JS SDK v7.20.0 and later, measurementId is optional
            const firebaseConfig = {
                apiKey: "AIzaSyDenbfLXVnpxWpn8SpOGOGYsVx0__VDtF4",
                authDomain: "cyber-learning-game.firebaseapp.com",
                databaseURL: "https://cyber-learning-game-default-rtdb.firebaseio.com",
                projectId: "cyber-learning-game",
                storageBucket: "cyber-learning-game.firebasestorage.app",
                messagingSenderId: "276191630065",
                appId: "1:276191630065:web:5ae7e9f9cd559aba0fcf50",
                measurementId: "G-Y52FKBM536"
            };

            // Initialize Firebase
            const app = initializeApp(firebaseConfig);
            const database = getDatabase(app);

            // retrive player data from firebase under "users" and reload page when data is updated
            const dbRef = ref(database);
            get(child(dbRef, 'users')).then((snapshot) => {
                if (snapshot.exists()) {
                    const userData = snapshot.val();
                    console.log(userData);
                    // add userData to page   
                    displayUserData(userData);
                } else {
                    console.log('No data available');
                }
            }).catch((error) => {
                console.error(error);
            });    

            function displayUserData(userData) {
                const playerDataDiv = document.getElementById('player-data');

                if (!userData || Object.keys(userData).length === 0) {
                    playerDataDiv.innerHTML = '<p>No player data available</p>';
                    return;
                }

                let html = '<div style="font-family: Arial, sans-serif;">';

                // Add summary statistics
                const totalUsers = Object.keys(userData).length;
                html += `
                    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; text-align: center;">
                        <h2 style="margin: 0; font-size: 24px;">📊 Player Statistics</h2>
                        <p style="margin: 10px 0 0 0; font-size: 18px;">Total Players: <strong>${totalUsers}</strong></p>
                    </div>
                `;

                // Display each user's data
                Object.keys(userData).forEach(userId => {
                    const user = userData[userId];
                    html += formatUserData(userId, user);
                });

                html += '</div>';
                playerDataDiv.innerHTML = html;
            }

            function formatUserData(userId, user) {
                const shortUserId = userId.replace('user_', '').substring(0, 8) + '...';
                const quizCount = user.quizzes ? Object.keys(user.quizzes).length : 0;
                const totalCoins = user.totalCoins || 0;
                const totalTokens = user.totalTokens || 0;

                let html = `
                    <div style="border: 1px solid #e0e0e0; margin: 20px 0; padding: 20px; border-radius: 12px; background: white; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; flex-wrap: wrap;">
                            <h3 style="color: #333; margin: 0; font-size: 18px;">👤 ${shortUserId}</h3>
                            <div style="display: flex; gap: 15px; flex-wrap: wrap;">
                                <span style="background: #4CAF50; color: white; padding: 5px 12px; border-radius: 20px; font-size: 12px; font-weight: bold;">
                                    🪙 ${totalCoins} Coins
                                </span>
                                <span style="background: #2196F3; color: white; padding: 5px 12px; border-radius: 20px; font-size: 12px; font-weight: bold;">
                                    🎯 ${totalTokens} Tokens
                                </span>
                                <span style="background: #FF9800; color: white; padding: 5px 12px; border-radius: 20px; font-size: 12px; font-weight: bold;">
                                    📝 ${quizCount} Quizzes
                                </span>
                            </div>
                        </div>
                `;

                if (user.quizzes && Object.keys(user.quizzes).length > 0) {
                    html += '<div style="margin-top: 15px;">';
                    html += '<h4 style="color: #555; margin-bottom: 10px; font-size: 16px;">📋 Quiz History</h4>';

                    // Sort quizzes by answered time (most recent first)
                    const sortedQuizzes = Object.entries(user.quizzes).sort((a, b) => {
                        return (b[1].answeredTime || 0) - (a[1].answeredTime || 0);
                    });

                    sortedQuizzes.forEach(([quizKey, quiz]) => {
                        html += formatQuizData(quizKey, quiz);
                    });

                    html += '</div>';
                } else {
                    html += '<p style="color: #666; font-style: italic;">No quiz data available</p>';
                }

                html += '</div>';
                return html;
            }

            function formatQuizData(quizKey, quiz) {
                const shortQuizKey = quizKey.length > 20 ? quizKey.substring(0, 20) + '...' : quizKey;
                const answeredTime = quiz.answeredTime ? formatTimestamp(quiz.answeredTime) : 'Unknown';
                const tokensEarned = quiz.tokensEarned || 0;
                const quizId = quiz.quizId || 'N/A';
                const startedFrom = quiz.staredFrom || quiz.startedFrom || 'Unknown';

                // Calculate score
                const answers = quiz.answers || [];
                const correctAnswers = answers.filter(answer => answer.isCorrect).length;
                const totalQuestions = answers.length;
                const scorePercentage = totalQuestions > 0 ? Math.round((correctAnswers / totalQuestions) * 100) : 0;

                // Determine score color
                let scoreColor = '#f44336'; // Red for low scores
                if (scorePercentage >= 80) scoreColor = '#4CAF50'; // Green for high scores
                else if (scorePercentage >= 60) scoreColor = '#FF9800'; // Orange for medium scores

                let html = `
                    <div style="border-left: 4px solid ${scoreColor}; background: #f9f9f9; margin: 10px 0; padding: 15px; border-radius: 8px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px; flex-wrap: wrap;">
                            <div>
                                <strong style="color: #333;">Quiz ${quizId}</strong>
                                <span style="color: #666; font-size: 12px; margin-left: 10px;">ID: ${shortQuizKey}</span>
                            </div>
                            <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                                <span style="background: ${scoreColor}; color: white; padding: 3px 8px; border-radius: 12px; font-size: 11px; font-weight: bold;">
                                    ${correctAnswers}/${totalQuestions} (${scorePercentage}%)
                                </span>
                                <span style="background: #9C27B0; color: white; padding: 3px 8px; border-radius: 12px; font-size: 11px; font-weight: bold;">
                                    +${tokensEarned} tokens
                                </span>
                            </div>
                        </div>

                        <div style="display: flex; justify-content: space-between; align-items: center; font-size: 12px; color: #666; flex-wrap: wrap;">
                            <span>📅 ${answeredTime}</span>
                            <span>🚀 Started from: ${startedFrom}</span>
                        </div>

                        ${formatAnswerDetails(answers)}
                    </div>
                `;

                return html;
            }

            function formatAnswerDetails(answers) {
                if (!answers || answers.length === 0) {
                    return '<p style="color: #666; font-size: 12px; margin-top: 10px;">No answer details available</p>';
                }

                let html = '<div style="margin-top: 10px; padding-top: 10px; border-top: 1px solid #e0e0e0;">';
                html += '<details style="cursor: pointer;">';
                html += '<summary style="font-size: 12px; color: #666; font-weight: bold;">📝 Answer Details</summary>';
                html += '<div style="margin-top: 8px; display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 8px;">';

                answers.forEach(answer => {
                    const isCorrect = answer.isCorrect;
                    const icon = isCorrect ? '✅' : '❌';
                    const bgColor = isCorrect ? '#e8f5e8' : '#ffeaea';
                    const borderColor = isCorrect ? '#4CAF50' : '#f44336';

                    html += `
                        <div style="background: ${bgColor}; border: 1px solid ${borderColor}; padding: 8px; border-radius: 6px; font-size: 11px;">
                            <div style="font-weight: bold; margin-bottom: 4px;">${icon} ${answer.questionId}</div>
                            <div>Correct: ${answer.answer}, User: ${answer.userAnswer}</div>
                        </div>
                    `;
                });

                html += '</div></details></div>';
                return html;
            }

            function formatTimestamp(timestamp) {
                try {
                    const date = new Date(timestamp);

                    // Check if the date is valid
                    if (isNaN(date.getTime())) {
                        return 'Invalid date';
                    }

                    const now = new Date();
                    const diffMs = now - date;
                    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
                    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
                    const diffMinutes = Math.floor(diffMs / (1000 * 60));

                    // Format relative time
                    let relativeTime = '';
                    if (diffDays > 0) {
                        relativeTime = `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
                    } else if (diffHours > 0) {
                        relativeTime = `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
                    } else if (diffMinutes > 0) {
                        relativeTime = `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
                    } else {
                        relativeTime = 'Just now';
                    }

                    // Format absolute time
                    const options = {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit',
                        hour12: true
                    };
                    const absoluteTime = date.toLocaleDateString('en-US', options);

                    return `${relativeTime} (${absoluteTime})`;
                } catch (error) {
                    console.error('Error formatting timestamp:', error);
                    return 'Invalid date';
                }
            }

    </script>
</html>